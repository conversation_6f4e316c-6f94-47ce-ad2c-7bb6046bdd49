import 'package:flutter/material.dart';

class AnimatedSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String)? onChanged;
  final VoidCallback? onClear;
  final bool isSearching;

  const AnimatedSearchBar({
    super.key,
    required this.controller,
    this.hintText = '搜索...',
    this.onChanged,
    this.onClear,
    this.isSearching = false,
  });

  @override
  State<AnimatedSearchBar> createState() => _AnimatedSearchBarState();
}

class _AnimatedSearchBarState extends State<AnimatedSearchBar>
    with TickerProviderStateMixin {
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late AnimationController _overlayController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _overlayAnimation;
  
  bool _isFocused = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeOutCubic,
    ));

    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(AnimatedSearchBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果controller发生变化，确保状态同步
    if (oldWidget.controller != widget.controller) {
      // 如果当前处于展开状态，需要重新构建overlay
      if (_isFocused && _overlayEntry != null) {
        _removeOverlay();
        _createOverlay();
      }
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _focusNode.removeListener(_onFocusChanged);
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
    _focusNode.dispose();
    _animationController.dispose();
    _overlayController.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!mounted) return;

    if (_focusNode.hasFocus && !_isFocused) {
      _showExpandedSearchBar();
    } else if (!_focusNode.hasFocus && _isFocused) {
      _hideExpandedSearchBar();
    }
  }

  void _showExpandedSearchBar() {
    if (_isFocused) return; // 防止重复调用

    setState(() {
      _isFocused = true;
    });

    _animationController.forward();
    _createOverlay();
  }

  void _hideExpandedSearchBar() {
    if (!_isFocused) return; // 防止重复调用

    setState(() {
      _isFocused = false;
    });

    _animationController.reverse();
    _overlayController.reverse().then((_) {
      if (mounted) {
        _removeOverlay();
        // 确保动画控制器完全重置
        _resetAnimationControllers();
      }
    });
  }

  void _resetAnimationControllers() {
    // 重置动画控制器到初始状态
    _animationController.reset();
    _overlayController.reset();

    // 确保状态完全重置
    if (mounted) {
      setState(() {
        _isFocused = false;
      });
    }
  }

  void _createOverlay() {
    _removeOverlay();

    if (!mounted) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => _buildExpandedSearchOverlay(),
    );

    try {
      Overlay.of(context).insert(_overlayEntry!);
      _overlayController.forward();
    } catch (e) {
      // 如果插入overlay失败，清理状态
      _overlayEntry = null;
      _resetAnimationControllers();
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      try {
        _overlayEntry!.remove();
      } catch (e) {
        // 忽略移除overlay时的错误
      }
      _overlayEntry = null;
    }
  }

  Widget _buildExpandedSearchOverlay() {
    return AnimatedBuilder(
      animation: _overlayAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // 背景遮罩
            GestureDetector(
              onTap: () {
                if (_isFocused && mounted) {
                  _focusNode.unfocus();
                  _hideExpandedSearchBar();
                }
              },
              child: Container(
                color: Colors.black.withOpacity(0.3 * _overlayAnimation.value),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // 展开的搜索栏
            Positioned(
              top: MediaQuery.of(context).padding.top + 60,
              left: 20 * (1 - _overlayAnimation.value) + 16 * _overlayAnimation.value,
              right: 20 * (1 - _overlayAnimation.value) + 16 * _overlayAnimation.value,
              child: Transform.scale(
                scale: 0.9 + 0.1 * _overlayAnimation.value,
                child: Material(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  elevation: 8 * _overlayAnimation.value,
                  shadowColor: Colors.black.withOpacity(0.15),
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _focusNode,
                    autofocus: true,
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                      ),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(16),
                        child: widget.isSearching
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.blue[600]!,
                                  ),
                                ),
                              )
                            : Icon(
                                Icons.search_rounded,
                                color: Colors.blue[600],
                                size: 24,
                              ),
                      ),
                      suffixIcon: widget.controller.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear_rounded,
                                color: Colors.grey[400],
                                size: 24,
                              ),
                              onPressed: () {
                                widget.controller.clear();
                                widget.onClear?.call();
                              },
                            )
                          : IconButton(
                              icon: Icon(
                                Icons.close_rounded,
                                color: Colors.grey[400],
                                size: 24,
                              ),
                              onPressed: () {
                                if (_isFocused && mounted) {
                                  _focusNode.unfocus();
                                  _hideExpandedSearchBar();
                                }
                              },
                            ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 18,
                      ),
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    onChanged: widget.onChanged,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _isFocused ? 0.95 : _scaleAnimation.value,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _isFocused ? Colors.blue[200]! : Colors.grey[200]!,
                width: _isFocused ? 2 : 1,
              ),
            ),
            child: Material(
              color: _isFocused ? Colors.grey[100] : Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              elevation: _isFocused ? 4 : 1,
              shadowColor: Colors.black.withOpacity(0.1),
              child: TextField(
                controller: widget.controller,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 15,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(12),
                    child: widget.isSearching
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.blue[600]!,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.search_rounded,
                            color: _isFocused ? Colors.blue[600] : Colors.grey[400],
                            size: 22,
                          ),
                  ),
                  suffixIcon: widget.controller.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear_rounded,
                            color: Colors.grey[400],
                            size: 22,
                          ),
                          onPressed: () {
                            widget.controller.clear();
                            widget.onClear?.call();
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                ),
                style: const TextStyle(fontSize: 15),
                onChanged: (value) {
                  setState(() {}); // 更新UI以显示/隐藏清除按钮
                  widget.onChanged?.call(value);
                },
              ),
            ),
          ),
        );
      },
    );
  }
} 